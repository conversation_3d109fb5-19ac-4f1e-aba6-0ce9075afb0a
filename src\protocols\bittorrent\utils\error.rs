//! BitTorrent模块统一错误类型
use thiserror::Error;

#[derive(Debug, Error)]
pub enum BitTorrentError {
    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),
    #[error("网络错误: {0}")]
    Network(String),
    #[error("协议错误: {0}")]
    Protocol(String),
    #[error("Tracker错误: {0}")]
    Tracker(String),
    #[error("DHT错误: {0}")]
    Dht(String),
    #[error("分片错误: {0}")]
    Piece(String),
    #[error("编码错误: {0}")]
    EncodeError(String),
    #[error("解码错误: {0}")]
    DecodeError(String),
    #[error("未知错误: {0}")]
    Other(String),
    #[error(transparent)]
    Anyhow(#[from] anyhow::Error),
}

impl From<String> for BitTorrentError {
    fn from(err: String) -> Self {
        BitTorrentError::Other(err)
    }
}

impl From<&str> for BitTorrentError {
    fn from(err: &str) -> Self {
        BitTorrentError::Other(err.to_string())
    }
}
