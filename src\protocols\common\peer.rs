use std::net::SocketAddr;
use std::time::{Duration, Instant};
use async_trait::async_trait;
use anyhow::{Result, anyhow};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use tokio::time::timeout;

use crate::core::p2p::peer::{<PERSON>eer, PeerInfo};

/// 速度计算器
pub struct SpeedCalculator {
    /// 时间窗口（秒）
    window_size: u64,
    /// 字节计数历史
    history: Vec<(Instant, u64)>,
    /// 总字节数
    total_bytes: u64,
}

impl SpeedCalculator {
    /// 创建新的速度计算器
    pub fn new(window_size: u64) -> Self {
        Self {
            window_size,
            history: Vec::new(),
            total_bytes: 0,
        }
    }

    /// 添加字节数
    pub fn add_bytes(&mut self, bytes: u64) {
        let now = Instant::now();
        self.history.push((now, bytes));
        self.total_bytes += bytes;

        // 清理过期的历史记录
        let cutoff = now - Duration::from_secs(self.window_size);

        // 收集需要删除的索引和字节数
        let mut to_remove = Vec::new();

        // 先收集需要删除的项
        for (i, (time, bytes)) in self.history.iter().enumerate() {
            if *time < cutoff {
                to_remove.push((i, *bytes));
            } else {
                break;
            }
        }

        // 从后向前删除，避免索引变化
        for (idx, bytes) in to_remove.iter().rev() {
            self.history.remove(*idx);
            self.total_bytes -= bytes;
        }
    }

    /// 获取当前速度（字节/秒）
    pub fn get_speed(&self) -> u64 {
        if self.history.is_empty() {
            return 0;
        }

        let now = Instant::now();
        let oldest = self.history.first().unwrap().0;
        let elapsed = now.duration_since(oldest).as_secs_f64();

        if elapsed <= 0.0 {
            return 0;
        }

        (self.total_bytes as f64 / elapsed) as u64
    }
}

/// 通用对等点连接实现
pub struct CommonPeer {
    /// 对等点信息
    pub peer_info: PeerInfo,
    /// TCP连接
    pub socket: Option<TcpStream>,
    /// 最后活动时间
    pub last_activity: Instant,
    /// 下载计数器
    pub download_counter: u64,
    /// 上传计数器
    pub upload_counter: u64,
    /// 下载速度计算器
    pub download_speed_calculator: SpeedCalculator,
    /// 上传速度计算器
    pub upload_speed_calculator: SpeedCalculator,
    /// 连接超时时间
    pub timeout: Duration,
    /// 总分片数
    pub num_pieces: u32,
    /// 下载速率限制 (字节/秒，0表示无限制)
    pub download_rate_limit: u64,
}

impl CommonPeer {
    /// 创建新的通用对等点连接
    pub fn new(addr: SocketAddr, timeout_secs: u64) -> Self {
        let timeout_duration = Duration::from_secs(timeout_secs);

        let peer_info = PeerInfo {
            id: None,
            addr,
            client_name: None,
            connected: false,
            last_seen: Instant::now(),
            downloaded: 0,
            uploaded: 0,
            download_speed: 0,
            upload_speed: 0,
        };

        Self {
            peer_info,
            socket: None,
            last_activity: Instant::now(),
            download_counter: 0,
            upload_counter: 0,
            download_speed_calculator: SpeedCalculator::new(5), // 5秒平均速度
            upload_speed_calculator: SpeedCalculator::new(5),   // 5秒平均速度
            timeout: timeout_duration,
            num_pieces: 0, // 默认为0，需要在创建后设置
            download_rate_limit: 0, // 默认无限制
        }
    }

    /// 设置对等点ID
    pub fn set_peer_id(&mut self, id: Vec<u8>) {
        self.peer_info.id = Some(id);
    }

    /// 设置客户端名称
    pub fn set_client_name(&mut self, name: String) {
        self.peer_info.client_name = Some(name);
    }

    /// 设置总分片数
    pub fn set_num_pieces(&mut self, num_pieces: u32) {
        self.num_pieces = num_pieces;
    }
    
    /// 设置下载速率限制
    pub fn set_download_rate_limit(&mut self, limit: u64) {
        self.download_rate_limit = limit;
    }

    /// 更新活动时间
    fn update_activity(&mut self) {
        self.last_activity = Instant::now();
        self.peer_info.last_seen = self.last_activity;
    }

    /// 更新下载统计
    pub fn update_download_stats(&mut self, bytes: usize) {
        self.download_counter += bytes as u64;
        self.peer_info.downloaded += bytes as u64;
        self.download_speed_calculator.add_bytes(bytes as u64);
        self.peer_info.download_speed = self.download_speed_calculator.get_speed();
    }

    /// 更新上传统计
    pub fn update_upload_stats(&mut self, bytes: usize) {
        self.upload_counter += bytes as u64;
        self.peer_info.uploaded += bytes as u64;
        self.upload_speed_calculator.add_bytes(bytes as u64);
        self.peer_info.upload_speed = self.upload_speed_calculator.get_speed();
    }
}

#[async_trait]
impl Peer for CommonPeer {
    fn info(&self) -> PeerInfo {
        self.peer_info.clone()
    }

    async fn connect(&mut self) -> Result<()> {
        if self.peer_info.connected {
            return Ok(());
        }

        // 连接到对等点
        let socket = match timeout(self.timeout, TcpStream::connect(self.peer_info.addr)).await {
            Ok(Ok(socket)) => socket,
            Ok(Err(e)) => return Err(anyhow!("Failed to connect to peer: {}", e)),
            Err(_) => return Err(anyhow!("Connection to peer timed out")),
        };

        // 设置socket选项
        socket.set_nodelay(true)?;

        self.socket = Some(socket);
        self.peer_info.connected = true;
        self.update_activity();

        Ok(())
    }

    async fn disconnect(&mut self) -> Result<()> {
        if let Some(socket) = self.socket.take() {
            drop(socket);
        }

        self.peer_info.connected = false;
        Ok(())
    }

    async fn send_message(&mut self, message: &[u8]) -> Result<()> {
        let socket = self.socket.as_mut()
            .ok_or_else(|| anyhow!("Not connected to peer"))?;

        match timeout(self.timeout, socket.write_all(message)).await {
            Ok(Ok(_)) => {
                self.update_activity();
                self.update_upload_stats(message.len());
                Ok(())
            },
            Ok(Err(e)) => {
                self.peer_info.connected = false;
                Err(anyhow!("Failed to send message: {}", e))
            },
            Err(_) => {
                self.peer_info.connected = false;
                Err(anyhow!("Send message timed out"))
            },
        }
    }

    async fn receive_message(&mut self) -> Result<Vec<u8>> {
        let mut len_bytes = vec![0; 4];
        let socket = self.socket.as_mut().ok_or_else(|| anyhow!("Socket not connected"))?;

        match timeout(self.timeout, socket.read_exact(&mut len_bytes)).await {
            Ok(Ok(0)) => return Err(anyhow!("Peer closed connection")), // 连接关闭
            Ok(Ok(_)) => {},
            Ok(Err(e)) => return Err(anyhow!("Failed to read message length: {}", e)),
            Err(_) => return Err(anyhow!("Read message length timed out")),
        }

        let message_len = u32::from_be_bytes(len_bytes.try_into().unwrap()) as usize;

        if message_len == 0 { // Keep-alive 消息
            self.update_activity();
            return Ok(vec![]);
        }

        let mut message_bytes = vec![0; message_len];
        match timeout(self.timeout, socket.read_exact(&mut message_bytes)).await {
            Ok(Ok(_)) => {},
            Ok(Err(e)) => return Err(anyhow!("Failed to read message: {}", e)),
            Err(_) => return Err(anyhow!("Read message timed out")),
        }

        self.update_activity();
        Ok(message_bytes)
    }

    fn is_connected(&self) -> bool {
        self.peer_info.connected
    }

    fn has_piece(&self, _piece_index: u32) -> bool {
        unimplemented!("has_piece should be implemented by specific peer protocols");
    }

    fn is_choking(&self) -> bool {
        unimplemented!("is_choking should be implemented by specific peer protocols");
    }

    /// 获取当前下载速度（字节/秒）
    /// 
    /// 返回值单位：字节/秒
    fn download_speed(&self) -> u64 {
        self.peer_info.download_speed
    }

    /// 获取当前上传速度（字节/秒）
    /// 
    /// 返回值单位：字节/秒
    fn upload_speed(&self) -> u64 {
        self.peer_info.upload_speed
    }

    /// 获取已下载的总字节数
    /// 
    /// 返回值单位：字节
    fn downloaded(&self) -> u64 {
        self.peer_info.downloaded
    }

    /// 获取已上传的总字节数
    ///
    /// 返回值单位：字节
    fn uploaded(&self) -> u64 {
        self.peer_info.uploaded
    }

    /// 添加分片到对等点的拥有列表
    async fn add_piece(&mut self, piece_index: u32) -> Result<()> {
        // CommonPeer是通用实现，不维护分片状态
        // 这个方法在具体的协议实现中会被重写
        Ok(())
    }

    /// 处理分片请求
    async fn handle_request(&mut self, piece_index: u32, offset: u32, length: u32) -> Result<()> {
        // CommonPeer是通用实现，不处理协议特定的请求
        // 这个方法在具体的协议实现中会被重写
        Err(anyhow::anyhow!("handle_request not implemented for CommonPeer"))
    }

    /// 处理取消请求
    async fn handle_cancel(&mut self, piece_index: u32, offset: u32, length: u32) -> Result<()> {
        // CommonPeer是通用实现，不处理协议特定的取消请求
        // 这个方法在具体的协议实现中会被重写
        Ok(())
    }

    /// 设置对等点的位图
    async fn set_bitfield(&mut self, bitfield: Vec<u8>) -> Result<()> {
        // CommonPeer是通用实现，不维护位图状态
        // 这个方法在具体的协议实现中会被重写
        Ok(())
    }

    /// 设置对等点是否阻塞我们
    async fn set_peer_choking(&mut self, choking: bool) -> Result<()> {
        // CommonPeer是通用实现，不维护阻塞状态
        // 这个方法在具体的协议实现中会被重写
        Ok(())
    }

    /// 设置对等点是否对我们感兴趣
    async fn set_peer_interested(&mut self, interested: bool) -> Result<()> {
        // CommonPeer是通用实现，不维护兴趣状态
        // 这个方法在具体的协议实现中会被重写
        Ok(())
    }

    /// 获取对等点拥有的所有分片索引
    fn pieces(&self) -> Vec<u32> {
        // CommonPeer是通用实现，不维护分片状态
        // 这个方法在具体的协议实现中会被重写
        Vec::new()
    }

    /// 处理上传请求
    async fn process_uploads(&mut self) -> Result<()> {
        // CommonPeer是通用实现，不处理上传
        // 这个方法在具体的协议实现中会被重写
        Ok(())
    }

    /// 发送BitTorrent消息
    async fn send_bt_message(&mut self, message: crate::protocols::bittorrent::message::BitTorrentMessage) -> Result<()> {
        // CommonPeer是通用实现，不处理BitTorrent特定消息
        // 这个方法在具体的协议实现中会被重写
        Err(anyhow::anyhow!("send_bt_message not implemented for CommonPeer"))
    }

    /// 设置上传速率限制
    fn set_upload_rate_limit(&mut self, limit: Option<u64>) {
        // CommonPeer是通用实现，不维护上传速率限制
        // 这个方法在具体的协议实现中会被重写
    }

    /// 获取上传速率限制
    fn get_upload_rate_limit(&self) -> Option<u64> {
        // CommonPeer是通用实现，不维护上传速率限制
        // 这个方法在具体的协议实现中会被重写
        None
    }
}
