use anyhow::{Result, anyhow};
use std::sync::Arc;
use tracing::{debug, info};

use crate::protocols::bittorrent::torrent::{parse_torrent_file, TorrentInfo};
use crate::protocols::bittorrent::tracker_manager::TrackerManager;
use crate::protocols::bittorrent::dht_manager::DHTManager;
use crate::protocols::bittorrent::peer_manager::PeerManager;
use crate::protocols::bittorrent::webseed::{WebSeedManager, WebSeedConfig};
use crate::protocols::bittorrent::extensions::{MagnetLinkParser, magnet::MagnetLinkInfo};
use crate::utils::HttpClient;
use crate::protocols::bittorrent::piece_manager::PieceSelectionStrategy;

use super::core::BitTorrentProtocol;

impl BitTorrentProtocol {
    /// Parse the torrent file or magnet link
    pub(crate) async fn parse_torrent(&mut self) -> Result<()> {
        if self.url.starts_with("magnet:") {
            // Parse magnet link using the new parser
            let magnet_info = MagnetLinkParser::parse(&self.url)?;

            // 创建TorrentInfo
            let torrent_info = self.create_torrent_info_from_magnet(magnet_info)?;

            self.torrent_info = Some(torrent_info);

            // 设置需要元数据交换标志
            self.needs_metadata = true;
        } else {
            // Parse torrent file
            self.torrent_info = Some(parse_torrent_file(&self.url).await?);
        }

        Ok(())
    }

    /// Create TorrentInfo from MagnetLinkInfo
    fn create_torrent_info_from_magnet(&self, magnet_info: MagnetLinkInfo) -> Result<TorrentInfo> {
        // 创建TorrentInfo
        let torrent_info = TorrentInfo {
            name: magnet_info.display_name.unwrap_or_else(|| "Unknown".to_string()),
            announce: magnet_info.tracker_urls.first().cloned(),
            announce_list: Some(magnet_info.tracker_urls.into_iter().map(|url| vec![url]).collect()),
            creation_date: None,
            comment: None,
            created_by: None,
            encoding: None,
            piece_length: 0, // 未知，直到获取元数据
            pieces: Vec::new(), // 未知，直到获取元数据
            files: None, // 未知，直到获取元数据
            length: 0, // 未知，直到获取元数据
            total_size: 0, // 未知，直到获取元数据
            info_hash: magnet_info.info_hash.to_vec(),
            info_hash_hex: magnet_info.info_hash_hex,
            info_hash_encoded: self.url_encode_bytes(&magnet_info.info_hash),
            url_list: Some(magnet_info.webseed_urls),
            http_seeds: None, // 未知，直到获取元数据
        };

        Ok(torrent_info)
    }

    /// URL encode bytes for tracker requests
    fn url_encode_bytes(&self, bytes: &[u8]) -> String {
        let mut encoded = String::with_capacity(bytes.len() * 3);
        for &byte in bytes {
            encoded.push('%');
            encoded.push_str(&hex::encode(&[byte]));
        }
        encoded
    }

    /// Initialize the tracker manager
    pub(crate) async fn init_tracker(&mut self) -> Result<()> {
        let update_interval = 300; // 5分钟更新间隔
        let mut tracker_manager = if let Some(config_manager) = &self.config_manager {
            TrackerManager::with_config_manager(
                self.peer_id.clone(),
                update_interval,
                Arc::clone(config_manager)
            )
        } else {
            TrackerManager::new(self.peer_id.clone(), update_interval)
        };
        if let Some(torrent_info) = &self.torrent_info {
            tracker_manager.init_trackers(torrent_info).await?;
        }
        self.tracker_manager = Some(Arc::new(tracker_manager));
        Ok(())
    }

    /// Initialize the DHT manager
    pub(crate) async fn init_dht(&mut self) -> Result<()> {
        // 检查是否启用DHT
        let use_dht = self.settings.enable_dht.unwrap_or(true);

        // 创建DHT管理器
        let mut dht_manager = DHTManager::new(&self.settings, use_dht, 300).await?; // 5分钟更新间隔

        // 初始化DHT管理器
        if use_dht {
            dht_manager.init().await?;

            // 添加BitTorrent协议的DHT事件监听器
            let torrent_info = self.torrent_info.clone();

            dht_manager.add_bittorrent_listener(move |info_hash, new_peers| {
                // 检查info_hash是否匹配
                if let Some(ref torrent_info) = torrent_info {
                    if torrent_info.info_hash == info_hash {
                        debug!("DHT found {} new peers for info_hash {}", new_peers.len(), hex::encode(&info_hash));
                    }
                }
                Ok(())
            }).await?;
        }

        self.dht_manager = Some(dht_manager);

        Ok(())
    }

    /// Initialize the peer manager
    pub(crate) fn init_peer_manager(&mut self) -> Result<()> {
        let piece_selection_strategy_str = self.settings.piece_selection_strategy.clone().unwrap_or_else(|| "rarest_first".to_string());
        let initial_strategy = match piece_selection_strategy_str.as_str() {
            "sequential" => PieceSelectionStrategy::Sequential,
            "rarest_first" => PieceSelectionStrategy::RarestFirst,
            "random_first" => PieceSelectionStrategy::RandomFirst,
            "end_game" => PieceSelectionStrategy::EndGame,
            _ => PieceSelectionStrategy::RarestFirst,
        };
        let num_pieces = self.torrent_info.as_ref().map_or(0, |info| info.pieces.len());
        let dht_manager = self.dht_manager.as_ref().map(|d| Arc::new(d.clone()));
        let piece_manager = self.piece_manager.as_ref().unwrap().clone();
        let mut peer_manager = PeerManager::new(
            self.peer_id.clone(),
            30,
            initial_strategy,
            num_pieces,
            dht_manager,
            piece_manager
        );
        self.peer_manager = Some(peer_manager);
        Ok(())
    }

    /// Initialize extensions
    pub(crate) async fn init_extensions(&mut self) -> Result<()> {
        // 检查是否有对等点管理器
        if self.peer_manager.is_none() {
            return Ok(());
        }

        // 初始化扩展协议
        {
            // 获取对等点管理器的可变引用
            let peer_manager = self.peer_manager.as_mut().unwrap();
            peer_manager.init_extensions().await?;
        }

        // 如果是磁力链接，初始化元数据交换扩展
        if self.url.starts_with("magnet:") && self.torrent_info.is_some() {
            // 使用临时变量避免多次可变借用
            let peer_manager_ref = self.peer_manager.as_mut().unwrap();
            let torrent_info = self.torrent_info.as_ref().unwrap();
            let info_hash = torrent_info.info_hash.clone();

            // 初始化元数据交换扩展
            peer_manager_ref.init_metadata_exchange(info_hash).await?;
        }

        debug!("BitTorrent extensions initialized");
        Ok(())
    }

    /// Initialize WebSeed support
    pub(crate) async fn init_webseed(&mut self) -> Result<()> {
        if !self.webseed_enabled {
            debug!("WebSeed support is disabled");
            return Ok(());
        }

        // 检查是否有种子信息
        let torrent_info = match &self.torrent_info {
            Some(info) => info,
            None => {
                debug!("No torrent information available for WebSeed initialization");
                return Ok(());
            }
        };

        // 检查种子是否包含WebSeed URL
        let has_url_seeds = torrent_info.url_list.as_ref().map_or(false, |list| !list.is_empty());
        let has_http_seeds = torrent_info.http_seeds.as_ref().map_or(false, |list| !list.is_empty());

        if !has_url_seeds && !has_http_seeds {
            debug!("No WebSeed URLs found in torrent");
            return Ok(());
        }

        // 创建WebSeed配置
        let webseed_config = WebSeedConfig::from_settings(&self.settings);

        // 更新启用状态
        let mut webseed_config = webseed_config;
        webseed_config.enabled = self.webseed_enabled;

        // 创建HTTP客户端
        let http_client = HttpClient::from_settings(&self.settings)?;

        // 创建WebSeed管理器
        let webseed_manager = WebSeedManager::new(
            http_client,
            webseed_config,
            Arc::new(torrent_info.clone()),
            self.bandwidth_scheduler.clone(),
            self.task_id,
        );

        // 添加WebSeed源
        webseed_manager.add_sources_from_torrent().await?;

        // 检查源可用性
        webseed_manager.check_sources_availability().await?;

        // 存储WebSeed管理器
        self.webseed_manager = Some(webseed_manager);

        info!("WebSeed support initialized with {} sources",
              self.webseed_manager.as_ref().unwrap().source_count().await);

        Ok(())
    }


}
